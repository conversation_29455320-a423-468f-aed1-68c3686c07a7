python -m vidur.main  \
--replica_config_device a100 \
--replica_config_model_name meta-llama/Meta-Llama-3-8B \
--cluster_config_num_replicas 1 \
--replica_config_tensor_parallel_size 1 \
--replica_config_num_pipeline_stages 1 \
--request_generator_config_type synthetic \
--synthetic_request_generator_config_num_requests 512  \
--length_generator_config_type trace \
--trace_request_length_generator_config_max_tokens 16384 \
--trace_request_length_generator_config_trace_file ./data/processed_traces/splitwise_conv.csv \
--interval_generator_config_type poisson \
--poisson_request_interval_generator_config_qps 6.45 \
--replica_scheduler_config_type sarathi  \
--sarathi_scheduler_config_batch_size_cap 512  \
--sarathi_scheduler_config_chunk_size 512 \
--random_forrest_execution_time_predictor_config_prediction_max_prefill_chunk_size 16384 \
--random_forrest_execution_time_predictor_config_prediction_max_batch_size 512 \
--random_forrest_execution_time_predictor_config_prediction_max_tokens_per_request 16384

以上是官网给出的运行 vidur 仿真的一个示例命令行指令，运行的结果在 simulator_output 下。

vidur 的论文里提到：
We validate the fidelity of Vidur on several LLMs and show that it estimates inference latency with less than 9% error across the range.

但是我发现一个问题：vidur 的 data/processed_traces 这些 csv 文件输入的都是 num_prefill_tokens 和 num_decode_tokens，而不是实际的文本，这样的数据没有办法让 vLLM 处理，因此我们进行复现的思路为：
1. 找到一个原始数据集
2. 用这个数据集输给 vllm，得到 vllm 的性能参数和 arrived_at, prefill_num, decode_num，保存下来
3. 将 arrived_at, prefill_num, decode_num 数据，保存为一个 csv 文件，再输给 vidur 进行模拟仿真最后进行对比

注意：你可以先查看 simulator_output 文件夹查看 vidur 仿真的结果，都包含了哪些性能指标
注意：vllm 是否自带 benchmark 简易数据集，可以用它自带的进行我们的复现，vllm 安装的路径为：/share_data/users/yukaifeng/LLMServing/vidur/env/lib/python3.10/site-packages/vllm，你可以查看它的项目结构
注意：你可以先进行搜索查看 vllm 如何获取关键指标数据，如何使用自带简易数据集（但是要注意版本，不同版本 api 函数可能略有不同，指标数据的获取方式应该不变，但是 benchmark 相关函数可能名字及调用方法有点区别）
注意：vllm 的运行通过终端启动，而不是通过 python 的 subprocess 方式启动；同时，vllm 的启动和关闭要放到一个 shell 脚本中，方便管理；vidur 的运行也放到一个 shell 脚本中，方便管理，vidur 脚本可以参考 run_vidur_simulation.sh
注意：你帮我创建的脚步要全部放到 validation 文件夹下，同时用一个配置文件管理公共参数，如本地模型路径（/share_data/llm_weights/Meta-Llama-3.1-8B）和端口号，整个复现实验部分要模块化，但不要过度封装